# 📸 Manejo de Imágenes en la API

## Descripción
La API soporta dos tipos de imágenes para productos:
- **URL**: Enlaces a imágenes externas
- **Base64**: Imágenes codificadas en base64 (ideal para fotos tomadas desde la app)

## Endpoints Disponibles

### 1. Listar imágenes
```
GET /api/imagenes-productos/
```

### 2. Subir foto en Base64 (📱 Para fotos tomadas)
```
POST /api/imagenes-productos/subir_foto/
Content-Type: application/json

{
    "producto": 1,
    "imagen_base64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
    "es_principal": true
}
```

### 3. Agregar imagen por URL
```
POST /api/imagenes-productos/agregar_url/
Content-Type: application/json

{
    "producto": 1,
    "url": "https://ejemplo.com/imagen.jpg",
    "es_principal": false
}
```

### 4. <PERSON><PERSON>r imagen (automático)
```
POST /api/imagenes-productos/
Content-Type: application/json

// Para Base64:
{
    "producto": 1,
    "imagen_base64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
    "es_principal": true
}

// Para URL:
{
    "producto": 1,
    "url": "https://ejemplo.com/imagen.jpg",
    "es_principal": false
}
```

## Formato Base64 Requerido

### Estructura:
```
data:image/[tipo];base64,[datos_codificados]
```

### Tipos soportados:
- `data:image/jpeg;base64,...`
- `data:image/png;base64,...`
- `data:image/gif;base64,...`
- `data:image/webp;base64,...`

### Ejemplo completo:
```javascript
// JavaScript - Capturar foto y convertir a base64
const input = document.getElementById('camera-input');
input.addEventListener('change', function(e) {
    const file = e.target.files[0];
    const reader = new FileReader();
    
    reader.onload = function(event) {
        const base64String = event.target.result;
        
        // Enviar a la API
        fetch('/api/imagenes-productos/subir_foto/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                producto: 1,
                imagen_base64: base64String,
                es_principal: true
            })
        });
    };
    
    reader.readAsDataURL(file);
});
```

## Validaciones

### Base64:
- ✅ Formato válido: `data:image/...;base64,...`
- ✅ Tamaño máximo: 5MB
- ✅ Tipos permitidos: jpeg, png, gif, webp

### URL:
- ✅ Formato válido: http:// o https://
- ✅ Dominio válido

## Respuesta de la API

```json
{
    "id": 1,
    "producto": 1,
    "url": null,
    "imagen_base64": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...",
    "tipo_imagen": "BASE64",
    "es_principal": true,
    "fecha_creacion": "2025-08-29T15:30:00Z",
    "imagen_data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
}
```

## Filtros Disponibles

```
GET /api/imagenes-productos/?producto=1
GET /api/imagenes-productos/?es_principal=true
GET /api/imagenes-productos/?tipo_imagen=BASE64
GET /api/imagenes-productos/?tipo_imagen=URL
```

## Casos de Uso

### 📱 App Móvil - Tomar foto del producto:
1. Usuario toma foto con la cámara
2. App convierte imagen a base64
3. Envía POST a `/api/imagenes-productos/subir_foto/`

### 🌐 Web - Agregar imagen existente:
1. Usuario proporciona URL de imagen
2. Envía POST a `/api/imagenes-productos/agregar_url/`

### 📦 Inventario - Evidencia de movimiento:
1. Tomar foto del producto durante movimiento
2. Crear imagen con base64
3. Asociar imagen al movimiento de inventario
