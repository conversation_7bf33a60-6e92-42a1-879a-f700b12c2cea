from django.db import models
from django.utils import timezone


class Categoria(models.Model):
    """Categorías de productos"""
    nombre = models.CharField(max_length=255, unique=True)

    class Meta:
        db_table = 'categorias'
        verbose_name = 'Categoría'
        verbose_name_plural = 'Categorías'

    def __str__(self):
        return self.nombre


class Proveedor(models.Model):
    """Proveedores de productos"""
    nombre = models.CharField(max_length=255)

    class Meta:
        db_table = 'proveedores'
        verbose_name = 'Proveedor'
        verbose_name_plural = 'Proveedores'

    def __str__(self):
        return self.nombre


class Producto(models.Model):
    """Productos del inventario"""
    nombre = models.CharField(max_length=255)
    sku = models.CharField(max_length=255, unique=True)
    codigo_barras = models.CharField(max_length=255, unique=True)
    precio = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    categoria = models.ForeignKey(Categoria, on_delete=models.CASCADE, related_name='productos')
    proveedor = models.ForeignKey(Proveedor, on_delete=models.SET_NULL, null=True, blank=True, related_name='productos')
    imagen_principal = models.CharField(max_length=255, null=True, blank=True)
    activo = models.BooleanField(default=True)

    class Meta:
        db_table = 'productos'
        verbose_name = 'Producto'
        verbose_name_plural = 'Productos'
        indexes = [
            models.Index(fields=['nombre'], name='productos_index_0'),
        ]

    def __str__(self):
        return f"{self.nombre} ({self.sku})"


class ImagenProducto(models.Model):
    """Imágenes de productos"""
    producto = models.ForeignKey(Producto, on_delete=models.CASCADE, related_name='imagenes')
    url = models.CharField(max_length=255)
    es_principal = models.BooleanField(default=False)

    class Meta:
        db_table = 'imagenes_productos'
        verbose_name = 'Imagen de Producto'
        verbose_name_plural = 'Imágenes de Productos'
        indexes = [
            models.Index(fields=['producto'], name='imagenes_productos_index_1'),
        ]

    def __str__(self):
        return f"Imagen de {self.producto.nombre}"


class Ubicacion(models.Model):
    """Ubicaciones físicas del inventario"""
    nombre = models.CharField(max_length=255)

    class Meta:
        db_table = 'ubicaciones'
        verbose_name = 'Ubicación'
        verbose_name_plural = 'Ubicaciones'

    def __str__(self):
        return self.nombre


class Inventario(models.Model):
    """Stock actual por producto y ubicación"""
    producto = models.ForeignKey(Producto, on_delete=models.CASCADE, related_name='inventarios')
    ubicacion = models.ForeignKey(Ubicacion, on_delete=models.CASCADE, related_name='inventarios')
    cantidad = models.IntegerField(default=0)
    minimo = models.IntegerField(default=0)

    class Meta:
        db_table = 'inventario'
        verbose_name = 'Inventario'
        verbose_name_plural = 'Inventarios'
        unique_together = ['producto', 'ubicacion']
        indexes = [
            models.Index(fields=['producto', 'ubicacion'], name='inventario_index_2'),
        ]

    def __str__(self):
        return f"{self.producto.nombre} en {self.ubicacion.nombre}: {self.cantidad}"

    @property
    def necesita_restock(self):
        """Indica si el producto necesita restock"""
        return self.cantidad <= self.minimo


class Rol(models.Model):
    """Roles de usuario"""
    nombre = models.CharField(max_length=255, unique=True)

    class Meta:
        db_table = 'roles'
        verbose_name = 'Rol'
        verbose_name_plural = 'Roles'

    def __str__(self):
        return self.nombre


class Usuario(models.Model):
    """Usuarios del sistema"""
    nombre_usuario = models.CharField(max_length=255, unique=True)
    clave_hash = models.CharField(max_length=255)
    rol = models.ForeignKey(Rol, on_delete=models.CASCADE, related_name='usuarios')
    activo = models.BooleanField(default=True)

    class Meta:
        db_table = 'usuarios'
        verbose_name = 'Usuario'
        verbose_name_plural = 'Usuarios'

    def __str__(self):
        return self.nombre_usuario


class MovimientoInventario(models.Model):
    """Historial de movimientos de inventario"""
    TIPOS_MOVIMIENTO = [
        ('ENTRADA', 'Entrada'),
        ('SALIDA', 'Salida'),
        ('AJUSTE', 'Ajuste'),
        ('TRANSFERENCIA', 'Transferencia'),
    ]

    producto = models.ForeignKey(Producto, on_delete=models.CASCADE, related_name='movimientos')
    ubicacion = models.ForeignKey(Ubicacion, on_delete=models.CASCADE, related_name='movimientos')
    tipo = models.CharField(max_length=255, choices=TIPOS_MOVIMIENTO)
    cantidad = models.IntegerField()
    usuario = models.ForeignKey(Usuario, on_delete=models.SET_NULL, null=True, blank=True, related_name='movimientos')
    imagen = models.ForeignKey(ImagenProducto, on_delete=models.SET_NULL, null=True, blank=True, related_name='movimientos_evidencia')
    fecha = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'movimientos_inventario'
        verbose_name = 'Movimiento de Inventario'
        verbose_name_plural = 'Movimientos de Inventario'
        indexes = [
            models.Index(fields=['producto'], name='movimientos_inventario_index_3'),
            models.Index(fields=['ubicacion'], name='movimientos_inventario_index_4'),
            models.Index(fields=['fecha'], name='movimientos_inventario_index_5'),
        ]

    def __str__(self):
        return f"{self.tipo} - {self.producto.nombre} ({self.cantidad}) - {self.fecha.strftime('%Y-%m-%d %H:%M')}"
