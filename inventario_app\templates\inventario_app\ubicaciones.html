{% extends 'inventario_app/base.html' %}

{% block title %}Ubicaciones - Sistema de Inventario{% endblock %}

{% block page_title %}
    <i class="fas fa-map-marker-alt"></i>
    Gestión de Ubicaciones
{% endblock %}

{% block page_actions %}
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalUbicacion">
    <i class="fas fa-plus"></i>
    Nueva Ubicación
</button>
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-list"></i>
            Lista de Ubicaciones
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    {% for ubicacion in ubicaciones.results|default:ubicaciones %}
                    <tr>
                        <td>{{ ubicacion.id }}</td>
                        <td><strong>{{ ubicacion.nombre }}</strong></td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="editarUbicacion({{ ubicacion.id }}, '{{ ubicacion.nombre }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="eliminarUbicacion({{ ubicacion.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3" class="text-center text-muted">
                            <i class="fas fa-inbox"></i>
                            No hay ubicaciones registradas
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal para crear/editar ubicación -->
<div class="modal fade" id="modalUbicacion" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-map-marker-alt"></i>
                    <span id="tituloModal">Nueva Ubicación</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formUbicacion">
                    <div class="mb-3">
                        <label class="form-label">Nombre *</label>
                        <input type="text" class="form-control" id="nombre" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="guardarUbicacion()">
                    <i class="fas fa-save"></i>
                    Guardar
                </button>
            </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let ubicacionEditando = null;

function editarUbicacion(id, nombre) {
    ubicacionEditando = id;
    document.getElementById('tituloModal').textContent = 'Editar Ubicación';
    document.getElementById('nombre').value = nombre;
    new bootstrap.Modal(document.getElementById('modalUbicacion')).show();
}

function eliminarUbicacion(id) {
    if (confirm('¿Está seguro de que desea eliminar esta ubicación?')) {
        fetch(`/api/ubicaciones/${id}/`, {
            method: 'DELETE',
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error al eliminar la ubicación');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al eliminar la ubicación');
        });
    }
}

function guardarUbicacion() {
    const formData = {
        nombre: document.getElementById('nombre').value
    };

    const url = ubicacionEditando ? `/api/ubicaciones/${ubicacionEditando}/` : '/api/ubicaciones/';
    const method = ubicacionEditando ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            return response.json().then(data => {
                throw new Error(JSON.stringify(data));
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al guardar la ubicación: ' + error.message);
    });
}

// Limpiar formulario al cerrar modal
document.getElementById('modalUbicacion').addEventListener('hidden.bs.modal', function () {
    ubicacionEditando = null;
    document.getElementById('tituloModal').textContent = 'Nueva Ubicación';
    document.getElementById('formUbicacion').reset();
});
</script>
{% endblock %}
