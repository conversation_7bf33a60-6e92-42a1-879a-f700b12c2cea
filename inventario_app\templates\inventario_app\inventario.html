{% extends 'inventario_app/base.html' %}

{% block title %}Inventario - Sistema de Inventario{% endblock %}

{% block page_title %}
    <i class="fas fa-warehouse"></i>
    Control de Inventario
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-list"></i>
            Stock por Ubicación
        </h6>
    </div>
    <div class="card-body">
        <!-- Filtros -->
        <div class="row mb-3">
            <div class="col-md-4">
                <input type="text" class="form-control" placeholder="Buscar producto...">
            </div>
            <div class="col-md-3">
                <select class="form-control">
                    <option value="">Todas las ubicaciones</option>
                    {% for ubicacion in ubicaciones %}
                    <option value="{{ ubicacion.id }}">{{ ubicacion.nombre }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-control">
                    <option value="">Todos los estados</option>
                    <option value="bajo">Bajo stock</option>
                    <option value="normal">Stock normal</option>
                </select>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Producto</th>
                        <th>SKU</th>
                        <th>Ubicación</th>
                        <th>Cantidad</th>
                        <th>Mínimo</th>
                        <th>Estado</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in inventario.results|default:inventario %}
                    <tr>
                        <td><strong>{{ item.producto_nombre }}</strong></td>
                        <td><code>{{ item.producto_sku }}</code></td>
                        <td>{{ item.ubicacion_nombre }}</td>
                        <td>
                            <span class="badge bg-primary">{{ item.cantidad }}</span>
                        </td>
                        <td>{{ item.minimo }}</td>
                        <td>
                            {% if item.necesita_restock %}
                            <span class="badge bg-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                Bajo Stock
                            </span>
                            {% else %}
                            <span class="badge bg-success">
                                <i class="fas fa-check"></i>
                                Normal
                            </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            <i class="fas fa-inbox"></i>
                            No hay registros de inventario
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
