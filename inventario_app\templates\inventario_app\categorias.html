{% extends 'inventario_app/base.html' %}

{% block title %}Categorías - Sistema de Inventario{% endblock %}

{% block page_title %}
    <i class="fas fa-tags"></i>
    Gestión de Categorías
{% endblock %}

{% block page_actions %}
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalCategoria">
    <i class="fas fa-plus"></i>
    Nueva Categoría
</button>
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-list"></i>
            Lista de Categorías
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    {% for categoria in categorias.results|default:categorias %}
                    <tr>
                        <td>{{ categoria.id }}</td>
                        <td><strong>{{ categoria.nombre }}</strong></td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="editarCategoria({{ categoria.id }}, '{{ categoria.nombre }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="eliminarCategoria({{ categoria.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3" class="text-center text-muted">
                            <i class="fas fa-inbox"></i>
                            No hay categorías registradas
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal para crear/editar categoría -->
<div class="modal fade" id="modalCategoria" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tags"></i>
                    <span id="tituloModal">Nueva Categoría</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formCategoria">
                    <div class="mb-3">
                        <label class="form-label">Nombre *</label>
                        <input type="text" class="form-control" id="nombre" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="guardarCategoria()">
                    <i class="fas fa-save"></i>
                    Guardar
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let categoriaEditando = null;

function editarCategoria(id, nombre) {
    categoriaEditando = id;
    document.getElementById('tituloModal').textContent = 'Editar Categoría';
    document.getElementById('nombre').value = nombre;
    new bootstrap.Modal(document.getElementById('modalCategoria')).show();
}

function eliminarCategoria(id) {
    if (confirm('¿Está seguro de que desea eliminar esta categoría?')) {
        fetch(`/api/categorias/${id}/`, {
            method: 'DELETE',
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error al eliminar la categoría');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al eliminar la categoría');
        });
    }
}

function guardarCategoria() {
    const formData = {
        nombre: document.getElementById('nombre').value
    };

    const url = categoriaEditando ? `/api/categorias/${categoriaEditando}/` : '/api/categorias/';
    const method = categoriaEditando ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            return response.json().then(data => {
                throw new Error(JSON.stringify(data));
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al guardar la categoría: ' + error.message);
    });
}

// Limpiar formulario al cerrar modal
document.getElementById('modalCategoria').addEventListener('hidden.bs.modal', function () {
    categoriaEditando = null;
    document.getElementById('tituloModal').textContent = 'Nueva Categoría';
    document.getElementById('formCategoria').reset();
});
</script>
{% endblock %}
