from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import requests
import json
from django.conf import settings


# URL base de la API
API_BASE_URL = 'http://127.0.0.1:8000/api/'


def index(request):
    """Página principal del inventario"""
    return render(request, 'inventario_app/index.html')


def productos(request):
    """Vista para gestionar productos"""
    try:
        # Obtener productos de la API
        response = requests.get(f'{API_BASE_URL}productos/')
        productos_data = response.json() if response.status_code == 200 else []

        # Obtener categorías para el formulario
        cat_response = requests.get(f'{API_BASE_URL}categorias/')
        categorias = cat_response.json() if cat_response.status_code == 200 else []

        # Obtener proveedores para el formulario
        prov_response = requests.get(f'{API_BASE_URL}proveedores/')
        proveedores = prov_response.json() if prov_response.status_code == 200 else []

        context = {
            'productos': productos_data,
            'categorias': categorias,
            'proveedores': proveedores,
        }
        return render(request, 'inventario_app/productos.html', context)
    except Exception as e:
        context = {
            'error': f'Error al conectar con la API: {str(e)}',
            'productos': [],
            'categorias': [],
            'proveedores': [],
        }
        return render(request, 'inventario_app/productos.html', context)


def inventario(request):
    """Vista para gestionar inventario"""
    try:
        # Obtener inventario de la API
        response = requests.get(f'{API_BASE_URL}inventario/')
        inventario_data = response.json() if response.status_code == 200 else []

        # Obtener productos para el formulario
        prod_response = requests.get(f'{API_BASE_URL}productos/')
        productos = prod_response.json() if prod_response.status_code == 200 else []

        # Obtener ubicaciones para el formulario
        ubi_response = requests.get(f'{API_BASE_URL}ubicaciones/')
        ubicaciones = ubi_response.json() if ubi_response.status_code == 200 else []

        context = {
            'inventario': inventario_data,
            'productos': productos,
            'ubicaciones': ubicaciones,
        }
        return render(request, 'inventario_app/inventario.html', context)
    except Exception as e:
        context = {
            'error': f'Error al conectar con la API: {str(e)}',
            'inventario': [],
            'productos': [],
            'ubicaciones': [],
        }
        return render(request, 'inventario_app/inventario.html', context)


def movimientos(request):
    """Vista para gestionar movimientos de inventario"""
    try:
        # Obtener movimientos de la API
        response = requests.get(f'{API_BASE_URL}movimientos/')
        movimientos_data = response.json() if response.status_code == 200 else []

        # Obtener productos para el formulario
        prod_response = requests.get(f'{API_BASE_URL}productos/')
        productos = prod_response.json() if prod_response.status_code == 200 else []

        # Obtener ubicaciones para el formulario
        ubi_response = requests.get(f'{API_BASE_URL}ubicaciones/')
        ubicaciones = ubi_response.json() if ubi_response.status_code == 200 else []

        # Obtener usuarios para el formulario
        user_response = requests.get(f'{API_BASE_URL}usuarios/')
        usuarios = user_response.json() if user_response.status_code == 200 else []

        context = {
            'movimientos': movimientos_data,
            'productos': productos,
            'ubicaciones': ubicaciones,
            'usuarios': usuarios,
        }
        return render(request, 'inventario_app/movimientos.html', context)
    except Exception as e:
        context = {
            'error': f'Error al conectar con la API: {str(e)}',
            'movimientos': [],
            'productos': [],
            'ubicaciones': [],
            'usuarios': [],
        }
        return render(request, 'inventario_app/movimientos.html', context)


def categorias(request):
    """Vista para gestionar categorías"""
    try:
        response = requests.get(f'{API_BASE_URL}categorias/')
        categorias_data = response.json() if response.status_code == 200 else []

        context = {
            'categorias': categorias_data,
        }
        return render(request, 'inventario_app/categorias.html', context)
    except Exception as e:
        context = {
            'error': f'Error al conectar con la API: {str(e)}',
            'categorias': [],
        }
        return render(request, 'inventario_app/categorias.html', context)


def proveedores(request):
    """Vista para gestionar proveedores"""
    try:
        response = requests.get(f'{API_BASE_URL}proveedores/')
        proveedores_data = response.json() if response.status_code == 200 else []

        context = {
            'proveedores': proveedores_data,
        }
        return render(request, 'inventario_app/proveedores.html', context)
    except Exception as e:
        context = {
            'error': f'Error al conectar con la API: {str(e)}',
            'proveedores': [],
        }
        return render(request, 'inventario_app/proveedores.html', context)


def ubicaciones(request):
    """Vista para gestionar ubicaciones"""
    try:
        response = requests.get(f'{API_BASE_URL}ubicaciones/')
        ubicaciones_data = response.json() if response.status_code == 200 else []

        context = {
            'ubicaciones': ubicaciones_data,
        }
        return render(request, 'inventario_app/ubicaciones.html', context)
    except Exception as e:
        context = {
            'error': f'Error al conectar con la API: {str(e)}',
            'ubicaciones': [],
        }
        return render(request, 'inventario_app/ubicaciones.html', context)
