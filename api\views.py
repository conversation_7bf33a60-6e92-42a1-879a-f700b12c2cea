from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, F

from .models import (
    Categoria, Proveedor, Producto, ImagenProducto,
    Ubicacion, Inventario, Rol, Usuario, MovimientoInventario
)
from .serializers import (
    CategoriaSerializer, ProveedorSerializer, ProductoSerializer, ProductoListSerializer,
    ImagenProductoSerializer, UbicacionSerializer, InventarioSerializer,
    RolSerializer, UsuarioSerializer, UsuarioCreateSerializer, UsuarioChangePasswordSerializer,
    MovimientoInventarioSerializer, MovimientoInventarioCreateSerializer
)


class CategoriaViewSet(viewsets.ModelViewSet):
    queryset = Categoria.objects.all()
    serializer_class = CategoriaSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['nombre']
    ordering_fields = ['nombre']
    ordering = ['nombre']


class ProveedorViewSet(viewsets.ModelViewSet):
    queryset = Proveedor.objects.all()
    serializer_class = ProveedorSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['nombre']
    ordering_fields = ['nombre']
    ordering = ['nombre']


class ProductoViewSet(viewsets.ModelViewSet):
    queryset = Producto.objects.select_related('categoria', 'proveedor').prefetch_related('imagenes')
    serializer_class = ProductoSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['categoria', 'proveedor', 'activo']
    search_fields = ['nombre', 'sku', 'codigo_barras']
    ordering_fields = ['nombre', 'precio', 'categoria__nombre']
    ordering = ['nombre']

    def get_serializer_class(self):
        if self.action == 'list':
            return ProductoListSerializer
        return ProductoSerializer

    @action(detail=False, methods=['get'])
    def buscar(self, request):
        """Búsqueda avanzada de productos por nombre, categoría, SKU, imagen y código"""
        query = request.query_params.get('q', '')
        if not query:
            return Response({'error': 'Parámetro de búsqueda requerido'}, status=status.HTTP_400_BAD_REQUEST)

        productos = self.queryset.filter(
            Q(nombre__icontains=query) |
            Q(sku__icontains=query) |
            Q(codigo_barras__icontains=query) |
            Q(categoria__nombre__icontains=query)
        )

        serializer = ProductoListSerializer(productos, many=True)
        return Response(serializer.data)


class ImagenProductoViewSet(viewsets.ModelViewSet):
    queryset = ImagenProducto.objects.select_related('producto')
    serializer_class = ImagenProductoSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['producto', 'es_principal']


class UbicacionViewSet(viewsets.ModelViewSet):
    queryset = Ubicacion.objects.all()
    serializer_class = UbicacionSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['nombre']
    ordering_fields = ['nombre']
    ordering = ['nombre']


class InventarioViewSet(viewsets.ModelViewSet):
    queryset = Inventario.objects.select_related('producto', 'ubicacion')
    serializer_class = InventarioSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['ubicacion', 'producto']
    search_fields = ['producto__nombre', 'ubicacion__nombre']

    @action(detail=False, methods=['get'])
    def bajo_stock(self, request):
        """Productos que necesitan restock"""
        inventarios = self.queryset.filter(cantidad__lte=F('minimo'))
        serializer = self.get_serializer(inventarios, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def por_ubicacion(self, request):
        """Stock agrupado por ubicación"""
        ubicacion_id = request.query_params.get('ubicacion_id')
        if not ubicacion_id:
            return Response({'error': 'ubicacion_id requerido'}, status=status.HTTP_400_BAD_REQUEST)

        inventarios = self.queryset.filter(ubicacion_id=ubicacion_id)
        serializer = self.get_serializer(inventarios, many=True)
        return Response(serializer.data)


class RolViewSet(viewsets.ModelViewSet):
    queryset = Rol.objects.all()
    serializer_class = RolSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['nombre']
    ordering_fields = ['nombre']
    ordering = ['nombre']


class UsuarioViewSet(viewsets.ModelViewSet):
    queryset = Usuario.objects.select_related('rol')
    serializer_class = UsuarioSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['rol', 'activo']
    search_fields = ['nombre_usuario']
    ordering_fields = ['nombre_usuario']
    ordering = ['nombre_usuario']

    def get_serializer_class(self):
        if self.action == 'create':
            return UsuarioCreateSerializer
        elif self.action == 'cambiar_password':
            return UsuarioChangePasswordSerializer
        return UsuarioSerializer

    @action(detail=True, methods=['post'])
    def cambiar_password(self, request, pk=None):
        """Cambiar contraseña de un usuario específico"""
        usuario = self.get_object()
        serializer = UsuarioChangePasswordSerializer(
            data=request.data,
            context={'request': request, 'usuario': usuario}
        )

        if serializer.is_valid():
            serializer.save()
            return Response({'message': 'Contraseña cambiada exitosamente'})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MovimientoInventarioViewSet(viewsets.ModelViewSet):
    queryset = MovimientoInventario.objects.select_related('producto', 'ubicacion', 'usuario')
    serializer_class = MovimientoInventarioSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['producto', 'ubicacion', 'tipo', 'usuario']
    search_fields = ['producto__nombre', 'ubicacion__nombre']
    ordering_fields = ['fecha']
    ordering = ['-fecha']

    def get_serializer_class(self):
        if self.action == 'create':
            return MovimientoInventarioCreateSerializer
        return MovimientoInventarioSerializer

    @action(detail=False, methods=['get'])
    def por_producto(self, request):
        """Historial de movimientos de un producto específico"""
        producto_id = request.query_params.get('producto_id')
        if not producto_id:
            return Response({'error': 'producto_id requerido'}, status=status.HTTP_400_BAD_REQUEST)

        movimientos = self.queryset.filter(producto_id=producto_id)
        serializer = self.get_serializer(movimientos, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def resumen_diario(self, request):
        """Resumen de movimientos del día actual"""
        from django.utils import timezone
        hoy = timezone.now().date()

        movimientos = self.queryset.filter(fecha__date=hoy)
        serializer = self.get_serializer(movimientos, many=True)
        return Response(serializer.data)
