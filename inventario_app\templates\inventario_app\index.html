{% extends 'inventario_app/base.html' %}

{% block title %}Dashboard - Sistema de Inventario{% endblock %}

{% block page_title %}
    <i class="fas fa-chart-line"></i>
    Dashboard
{% endblock %}

{% block content %}
<div class="row">
    <!-- Tarjetas de resumen -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Productos Totales
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-productos">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Stock Total
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="stock-total">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Bajo Stock
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="bajo-stock">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Movimientos Hoy
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="movimientos-hoy">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Accesos rápidos -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-rocket"></i>
                    Accesos Rápidos
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="{% url 'inventario_app:productos' %}" class="btn btn-primary btn-block">
                            <i class="fas fa-plus"></i>
                            Nuevo Producto
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{% url 'inventario_app:movimientos' %}" class="btn btn-success btn-block">
                            <i class="fas fa-exchange-alt"></i>
                            Registrar Movimiento
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{% url 'inventario_app:inventario' %}" class="btn btn-info btn-block">
                            <i class="fas fa-search"></i>
                            Consultar Stock
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/api/" target="_blank" class="btn btn-secondary btn-block">
                            <i class="fas fa-code"></i>
                            Ver API
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle"></i>
                    Estado del Sistema
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>API Status</span>
                        <span id="api-status">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Base de Datos</span>
                        <span class="text-success">
                            <i class="fas fa-check-circle"></i> Conectada
                        </span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Última Actualización</span>
                        <span id="ultima-actualizacion">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Cargar estadísticas del dashboard
async function cargarEstadisticas() {
    try {
        // Cargar productos
        const productosResponse = await fetch('/api/productos/');
        const productos = await productosResponse.json();
        document.getElementById('total-productos').innerHTML = productos.count || productos.length || 0;

        // Cargar inventario
        const inventarioResponse = await fetch('/api/inventario/');
        const inventario = await inventarioResponse.json();
        const stockTotal = inventario.results ? 
            inventario.results.reduce((sum, item) => sum + item.cantidad, 0) :
            inventario.reduce((sum, item) => sum + item.cantidad, 0);
        document.getElementById('stock-total').innerHTML = stockTotal;

        // Cargar productos bajo stock
        const bajoStockResponse = await fetch('/api/inventario/bajo_stock/');
        const bajoStock = await bajoStockResponse.json();
        document.getElementById('bajo-stock').innerHTML = bajoStock.length || 0;

        // Cargar movimientos de hoy
        const movimientosResponse = await fetch('/api/movimientos/resumen_diario/');
        const movimientos = await movimientosResponse.json();
        document.getElementById('movimientos-hoy').innerHTML = movimientos.length || 0;

        // Estado de la API
        document.getElementById('api-status').innerHTML = 
            '<i class="fas fa-check-circle text-success"></i> Conectada';

        // Última actualización
        document.getElementById('ultima-actualizacion').innerHTML = 
            new Date().toLocaleTimeString();

    } catch (error) {
        console.error('Error cargando estadísticas:', error);
        document.getElementById('api-status').innerHTML = 
            '<i class="fas fa-times-circle text-danger"></i> Error';
    }
}

// Cargar estadísticas al cargar la página
document.addEventListener('DOMContentLoaded', cargarEstadisticas);

// Actualizar cada 30 segundos
setInterval(cargarEstadisticas, 30000);
</script>
{% endblock %}
