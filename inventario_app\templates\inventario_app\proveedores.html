{% extends 'inventario_app/base.html' %}

{% block title %}Proveedores - Sistema de Inventario{% endblock %}

{% block page_title %}
    <i class="fas fa-truck"></i>
    Gestión de Proveedores
{% endblock %}

{% block page_actions %}
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalProveedor">
    <i class="fas fa-plus"></i>
    Nuevo Proveedor
</button>
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-list"></i>
            Lista de Proveedores
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    {% for proveedor in proveedores.results|default:proveedores %}
                    <tr>
                        <td>{{ proveedor.id }}</td>
                        <td><strong>{{ proveedor.nombre }}</strong></td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="editarProveedor({{ proveedor.id }}, '{{ proveedor.nombre }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="eliminarProveedor({{ proveedor.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3" class="text-center text-muted">
                            <i class="fas fa-inbox"></i>
                            No hay proveedores registrados
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal para crear/editar proveedor -->
<div class="modal fade" id="modalProveedor" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-truck"></i>
                    <span id="tituloModal">Nuevo Proveedor</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formProveedor">
                    <div class="mb-3">
                        <label class="form-label">Nombre *</label>
                        <input type="text" class="form-control" id="nombre" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="guardarProveedor()">
                    <i class="fas fa-save"></i>
                    Guardar
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let proveedorEditando = null;

function editarProveedor(id, nombre) {
    proveedorEditando = id;
    document.getElementById('tituloModal').textContent = 'Editar Proveedor';
    document.getElementById('nombre').value = nombre;
    new bootstrap.Modal(document.getElementById('modalProveedor')).show();
}

function eliminarProveedor(id) {
    if (confirm('¿Está seguro de que desea eliminar este proveedor?')) {
        fetch(`/api/proveedores/${id}/`, {
            method: 'DELETE',
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error al eliminar el proveedor');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al eliminar el proveedor');
        });
    }
}

function guardarProveedor() {
    const formData = {
        nombre: document.getElementById('nombre').value
    };

    const url = proveedorEditando ? `/api/proveedores/${proveedorEditando}/` : '/api/proveedores/';
    const method = proveedorEditando ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            return response.json().then(data => {
                throw new Error(JSON.stringify(data));
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al guardar el proveedor: ' + error.message);
    });
}

// Limpiar formulario al cerrar modal
document.getElementById('modalProveedor').addEventListener('hidden.bs.modal', function () {
    proveedorEditando = null;
    document.getElementById('tituloModal').textContent = 'Nuevo Proveedor';
    document.getElementById('formProveedor').reset();
});
</script>
{% endblock %}
