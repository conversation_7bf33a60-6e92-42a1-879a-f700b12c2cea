from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    CategoriaViewSet, ProveedorViewSet, ProductoViewSet, ImagenProductoViewSet,
    UbicacionViewSet, InventarioViewSet, RolViewSet, UsuarioViewSet, MovimientoInventarioViewSet
)

# Crear el router y registrar los viewsets
router = DefaultRouter()
router.register(r'categorias', CategoriaViewSet)
router.register(r'proveedores', ProveedorViewSet)
router.register(r'productos', ProductoViewSet)
router.register(r'imagenes-productos', ImagenProductoViewSet)
router.register(r'ubicaciones', UbicacionViewSet)
router.register(r'inventario', InventarioViewSet)
router.register(r'roles', RolViewSet)
router.register(r'usuarios', UsuarioViewSet)
router.register(r'movimientos', MovimientoInventarioViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
