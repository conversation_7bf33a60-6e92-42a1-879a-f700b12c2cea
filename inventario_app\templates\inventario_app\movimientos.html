{% extends 'inventario_app/base.html' %}

{% block title %}Movimientos - Sistema de Inventario{% endblock %}

{% block page_title %}
    <i class="fas fa-exchange-alt"></i>
    Movimientos de Inventario
{% endblock %}

{% block page_actions %}
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalMovimiento">
    <i class="fas fa-plus"></i>
    Nuevo Movimiento
</button>
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-list"></i>
            Historial de Movimientos
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Fecha</th>
                        <th>Producto</th>
                        <th>Ubicación</th>
                        <th>Tipo</th>
                        <th>Cantidad</th>
                        <th>Usuario</th>
                    </tr>
                </thead>
                <tbody>
                    {% for movimiento in movimientos.results|default:movimientos %}
                    <tr>
                        <td>{{ movimiento.fecha|date:"d/m/Y H:i" }}</td>
                        <td><strong>{{ movimiento.producto_nombre }}</strong></td>
                        <td>{{ movimiento.ubicacion_nombre }}</td>
                        <td>
                            {% if movimiento.tipo == 'ENTRADA' %}
                            <span class="badge bg-success">{{ movimiento.tipo }}</span>
                            {% elif movimiento.tipo == 'SALIDA' %}
                            <span class="badge bg-danger">{{ movimiento.tipo }}</span>
                            {% elif movimiento.tipo == 'AJUSTE' %}
                            <span class="badge bg-warning">{{ movimiento.tipo }}</span>
                            {% else %}
                            <span class="badge bg-info">{{ movimiento.tipo }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if movimiento.tipo == 'ENTRADA' or movimiento.tipo == 'AJUSTE' %}
                            <span class="text-success">+{{ movimiento.cantidad }}</span>
                            {% else %}
                            <span class="text-danger">-{{ movimiento.cantidad }}</span>
                            {% endif %}
                        </td>
                        <td>{{ movimiento.usuario_nombre|default:"Sistema" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            <i class="fas fa-inbox"></i>
                            No hay movimientos registrados
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal para nuevo movimiento -->
<div class="modal fade" id="modalMovimiento" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exchange-alt"></i>
                    Nuevo Movimiento
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formMovimiento">
                    <div class="mb-3">
                        <label class="form-label">Producto *</label>
                        <select class="form-control" id="producto" required>
                            <option value="">Seleccionar producto</option>
                            {% for producto in productos %}
                            <option value="{{ producto.id }}">{{ producto.nombre }} ({{ producto.sku }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Ubicación *</label>
                        <select class="form-control" id="ubicacion" required>
                            <option value="">Seleccionar ubicación</option>
                            {% for ubicacion in ubicaciones %}
                            <option value="{{ ubicacion.id }}">{{ ubicacion.nombre }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Tipo de Movimiento *</label>
                        <select class="form-control" id="tipo" required>
                            <option value="">Seleccionar tipo</option>
                            <option value="ENTRADA">Entrada</option>
                            <option value="SALIDA">Salida</option>
                            <option value="AJUSTE">Ajuste</option>
                            <option value="TRANSFERENCIA">Transferencia</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Cantidad *</label>
                        <input type="number" class="form-control" id="cantidad" required min="1">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Usuario</label>
                        <select class="form-control" id="usuario">
                            <option value="">Sistema</option>
                            {% for usuario in usuarios %}
                            <option value="{{ usuario.id }}">{{ usuario.nombre_usuario }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="guardarMovimiento()">
                    <i class="fas fa-save"></i>
                    Registrar Movimiento
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function guardarMovimiento() {
    const formData = {
        producto: parseInt(document.getElementById('producto').value),
        ubicacion: parseInt(document.getElementById('ubicacion').value),
        tipo: document.getElementById('tipo').value,
        cantidad: parseInt(document.getElementById('cantidad').value),
        usuario: document.getElementById('usuario').value || null
    };

    fetch('/api/movimientos/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            return response.json().then(data => {
                throw new Error(JSON.stringify(data));
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al registrar el movimiento: ' + error.message);
    });
}

// Limpiar formulario al cerrar modal
document.getElementById('modalMovimiento').addEventListener('hidden.bs.modal', function () {
    document.getElementById('formMovimiento').reset();
});
</script>
{% endblock %}
