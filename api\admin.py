from django.contrib import admin
from .models import (
    Categoria, Proveedor, Producto, ImagenProducto,
    Ubicacion, Inventario, Rol, Usuario, MovimientoInventario
)


@admin.register(Categoria)
class CategoriaAdmin(admin.ModelAdmin):
    list_display = ['id', 'nombre']
    search_fields = ['nombre']
    ordering = ['nombre']


@admin.register(Proveedor)
class ProveedorAdmin(admin.ModelAdmin):
    list_display = ['id', 'nombre']
    search_fields = ['nombre']
    ordering = ['nombre']


@admin.register(Producto)
class ProductoAdmin(admin.ModelAdmin):
    list_display = ['id', 'nombre', 'sku', 'codigo_barras', 'precio', 'categoria', 'proveedor', 'activo']
    list_filter = ['categoria', 'proveedor', 'activo']
    search_fields = ['nombre', 'sku', 'codigo_barras']
    ordering = ['nombre']
    list_editable = ['activo']


@admin.register(ImagenProducto)
class ImagenProductoAdmin(admin.ModelAdmin):
    list_display = ['id', 'producto', 'url', 'es_principal']
    list_filter = ['es_principal']
    search_fields = ['producto__nombre']
    ordering = ['producto__nombre']


@admin.register(Ubicacion)
class UbicacionAdmin(admin.ModelAdmin):
    list_display = ['id', 'nombre']
    search_fields = ['nombre']
    ordering = ['nombre']


@admin.register(Inventario)
class InventarioAdmin(admin.ModelAdmin):
    list_display = ['id', 'producto', 'ubicacion', 'cantidad', 'minimo', 'necesita_restock']
    list_filter = ['ubicacion']
    search_fields = ['producto__nombre', 'ubicacion__nombre']
    ordering = ['producto__nombre']

    def necesita_restock(self, obj):
        return obj.necesita_restock
    necesita_restock.boolean = True
    necesita_restock.short_description = 'Necesita Restock'


@admin.register(Rol)
class RolAdmin(admin.ModelAdmin):
    list_display = ['id', 'nombre']
    search_fields = ['nombre']
    ordering = ['nombre']


@admin.register(Usuario)
class UsuarioAdmin(admin.ModelAdmin):
    list_display = ['id', 'nombre_usuario', 'rol', 'activo']
    list_filter = ['rol', 'activo']
    search_fields = ['nombre_usuario']
    ordering = ['nombre_usuario']
    list_editable = ['activo']


@admin.register(MovimientoInventario)
class MovimientoInventarioAdmin(admin.ModelAdmin):
    list_display = ['id', 'producto', 'ubicacion', 'tipo', 'cantidad', 'usuario', 'fecha']
    list_filter = ['tipo', 'ubicacion', 'fecha']
    search_fields = ['producto__nombre', 'usuario__nombre_usuario']
    ordering = ['-fecha']
    readonly_fields = ['fecha']
