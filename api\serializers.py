from rest_framework import serializers
from .models import (
    Categoria, Proveedor, Producto, ImagenProducto, 
    Ubicacion, Inventario, Rol, Usuario, MovimientoInventario
)


class CategoriaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Categoria
        fields = ['id', 'nombre']


class ProveedorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Proveedor
        fields = ['id', 'nombre']


class ImagenProductoSerializer(serializers.ModelSerializer):
    class Meta:
        model = ImagenProducto
        fields = ['id', 'producto', 'url', 'es_principal']


class ProductoSerializer(serializers.ModelSerializer):
    categoria_nombre = serializers.CharField(source='categoria.nombre', read_only=True)
    proveedor_nombre = serializers.CharField(source='proveedor.nombre', read_only=True)
    imagenes = ImagenProductoSerializer(many=True, read_only=True)
    
    class Meta:
        model = Producto
        fields = [
            'id', 'nombre', 'sku', 'codigo_barras', 'precio', 
            'categoria', 'categoria_nombre', 'proveedor', 'proveedor_nombre',
            'imagen_principal', 'activo', 'imagenes'
        ]


class ProductoListSerializer(serializers.ModelSerializer):
    """Serializer simplificado para listados de productos"""
    categoria_nombre = serializers.CharField(source='categoria.nombre', read_only=True)
    
    class Meta:
        model = Producto
        fields = ['id', 'nombre', 'sku', 'codigo_barras', 'categoria_nombre', 'imagen_principal']


class UbicacionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ubicacion
        fields = ['id', 'nombre']


class InventarioSerializer(serializers.ModelSerializer):
    producto_nombre = serializers.CharField(source='producto.nombre', read_only=True)
    producto_sku = serializers.CharField(source='producto.sku', read_only=True)
    ubicacion_nombre = serializers.CharField(source='ubicacion.nombre', read_only=True)
    necesita_restock = serializers.ReadOnlyField()
    
    class Meta:
        model = Inventario
        fields = [
            'id', 'producto', 'producto_nombre', 'producto_sku',
            'ubicacion', 'ubicacion_nombre', 'cantidad', 'minimo', 'necesita_restock'
        ]


class RolSerializer(serializers.ModelSerializer):
    class Meta:
        model = Rol
        fields = ['id', 'nombre']


class UsuarioSerializer(serializers.ModelSerializer):
    rol_nombre = serializers.CharField(source='rol.nombre', read_only=True)
    
    class Meta:
        model = Usuario
        fields = ['id', 'nombre_usuario', 'rol', 'rol_nombre', 'activo']
        extra_kwargs = {
            'clave_hash': {'write_only': True}
        }


class UsuarioCreateSerializer(serializers.ModelSerializer):
    """Serializer para crear usuarios con contraseña"""
    password = serializers.CharField(write_only=True)
    
    class Meta:
        model = Usuario
        fields = ['nombre_usuario', 'password', 'rol', 'activo']
    
    def create(self, validated_data):
        from django.contrib.auth.hashers import make_password

        password = validated_data.pop('password')
        # Encriptar la contraseña usando el sistema de hash de Django
        validated_data['clave_hash'] = make_password(password)
        return Usuario.objects.create(**validated_data)


class UsuarioChangePasswordSerializer(serializers.Serializer):
    """Serializer para cambiar contraseñas de usuarios existentes"""
    password_actual = serializers.CharField(write_only=True)
    password_nueva = serializers.CharField(write_only=True)
    password_confirmacion = serializers.CharField(write_only=True)

    def validate(self, data):
        if data['password_nueva'] != data['password_confirmacion']:
            raise serializers.ValidationError("Las contraseñas nuevas no coinciden")

        if len(data['password_nueva']) < 6:
            raise serializers.ValidationError("La contraseña debe tener al menos 6 caracteres")

        return data

    def validate_password_actual(self, value):
        usuario = self.context['request'].user if 'request' in self.context else None
        if usuario and hasattr(usuario, 'check_password'):
            if not usuario.check_password(value):
                raise serializers.ValidationError("La contraseña actual es incorrecta")
        return value

    def save(self):
        usuario = self.context.get('usuario')
        if usuario:
            usuario.set_password(self.validated_data['password_nueva'])
            usuario.save()
        return usuario


class MovimientoInventarioSerializer(serializers.ModelSerializer):
    producto_nombre = serializers.CharField(source='producto.nombre', read_only=True)
    ubicacion_nombre = serializers.CharField(source='ubicacion.nombre', read_only=True)
    usuario_nombre = serializers.CharField(source='usuario.nombre_usuario', read_only=True)
    
    class Meta:
        model = MovimientoInventario
        fields = [
            'id', 'producto', 'producto_nombre', 'ubicacion', 'ubicacion_nombre',
            'tipo', 'cantidad', 'usuario', 'usuario_nombre', 'imagen', 'fecha'
        ]
        read_only_fields = ['fecha']


class MovimientoInventarioCreateSerializer(serializers.ModelSerializer):
    """Serializer para crear movimientos de inventario"""
    
    class Meta:
        model = MovimientoInventario
        fields = ['producto', 'ubicacion', 'tipo', 'cantidad', 'usuario', 'imagen']
    
    def create(self, validated_data):
        movimiento = MovimientoInventario.objects.create(**validated_data)
        
        # Actualizar el inventario según el tipo de movimiento
        inventario, _ = Inventario.objects.get_or_create(
            producto=movimiento.producto,
            ubicacion=movimiento.ubicacion,
            defaults={'cantidad': 0, 'minimo': 0}
        )
        
        if movimiento.tipo in ['ENTRADA', 'AJUSTE']:
            inventario.cantidad += movimiento.cantidad
        elif movimiento.tipo == 'SALIDA':
            inventario.cantidad -= movimiento.cantidad
        
        inventario.save()
        return movimiento
