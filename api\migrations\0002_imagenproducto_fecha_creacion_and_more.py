# Generated by Django 5.2.5 on 2025-08-29 22:27

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='imagenproducto',
            name='fecha_creacion',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='imagenproducto',
            name='imagen_base64',
            field=models.TextField(blank=True, help_text='Imagen en formato base64', null=True),
        ),
        migrations.AddField(
            model_name='imagenproducto',
            name='tipo_imagen',
            field=models.CharField(choices=[('URL', 'URL'), ('BASE64', 'Base64')], default='URL', max_length=10),
        ),
        migrations.Alter<PERSON>ield(
            model_name='imagenproducto',
            name='url',
            field=models.CharField(blank=True, help_text='URL de la imagen', max_length=255, null=True),
        ),
    ]
