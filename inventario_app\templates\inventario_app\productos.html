{% extends 'inventario_app/base.html' %}

{% block title %}Productos - Sistema de Inventario{% endblock %}

{% block page_title %}
    <i class="fas fa-box"></i>
    Gestión de Productos
{% endblock %}

{% block page_actions %}
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalProducto">
    <i class="fas fa-plus"></i>
    Nuevo Producto
</button>
{% endblock %}

{% block content %}
<div class="card shadow">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-list"></i>
            Lista de Productos
        </h6>
    </div>
    <div class="card-body">
        <!-- Filtros -->
        <div class="row mb-3">
            <div class="col-md-4">
                <input type="text" class="form-control" id="buscarProducto" placeholder="Buscar por nombre, SKU o código...">
            </div>
            <div class="col-md-3">
                <select class="form-control" id="filtroCategoria">
                    <option value="">Todas las categorías</option>
                    {% for categoria in categorias %}
                    <option value="{{ categoria.id }}">{{ categoria.nombre }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-control" id="filtroActivo">
                    <option value="">Todos los estados</option>
                    <option value="true">Activos</option>
                    <option value="false">Inactivos</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary" onclick="aplicarFiltros()">
                    <i class="fas fa-search"></i>
                    Filtrar
                </button>
            </div>
        </div>

        <!-- Tabla de productos -->
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>SKU</th>
                        <th>Código de Barras</th>
                        <th>Precio</th>
                        <th>Categoría</th>
                        <th>Proveedor</th>
                        <th>Estado</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody id="tablaProductos">
                    {% for producto in productos.results|default:productos %}
                    <tr>
                        <td>{{ producto.id }}</td>
                        <td>
                            <strong>{{ producto.nombre }}</strong>
                            {% if producto.imagen_principal %}
                            <br><small class="text-muted">
                                <i class="fas fa-image"></i> Con imagen
                            </small>
                            {% endif %}
                        </td>
                        <td><code>{{ producto.sku }}</code></td>
                        <td><code>{{ producto.codigo_barras }}</code></td>
                        <td>${{ producto.precio }}</td>
                        <td>
                            <span class="badge bg-info">{{ producto.categoria_nombre }}</span>
                        </td>
                        <td>{{ producto.proveedor_nombre|default:"Sin proveedor" }}</td>
                        <td>
                            {% if producto.activo %}
                            <span class="badge bg-success">Activo</span>
                            {% else %}
                            <span class="badge bg-danger">Inactivo</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="editarProducto({{ producto.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info" 
                                        onclick="verDetalles({{ producto.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="eliminarProducto({{ producto.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            <i class="fas fa-inbox"></i>
                            No hay productos registrados
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal para crear/editar producto -->
<div class="modal fade" id="modalProducto" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-box"></i>
                    <span id="tituloModal">Nuevo Producto</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formProducto">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nombre *</label>
                                <input type="text" class="form-control" id="nombre" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">SKU *</label>
                                <input type="text" class="form-control" id="sku" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Código de Barras *</label>
                                <input type="text" class="form-control" id="codigo_barras" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Precio</label>
                                <input type="number" step="0.01" class="form-control" id="precio" value="0">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Categoría *</label>
                                <select class="form-control" id="categoria" required>
                                    <option value="">Seleccionar categoría</option>
                                    {% for categoria in categorias %}
                                    <option value="{{ categoria.id }}">{{ categoria.nombre }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Proveedor</label>
                                <select class="form-control" id="proveedor">
                                    <option value="">Sin proveedor</option>
                                    {% for proveedor in proveedores %}
                                    <option value="{{ proveedor.id }}">{{ proveedor.nombre }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Imagen Principal (URL)</label>
                                <input type="url" class="form-control" id="imagen_principal" placeholder="https://...">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="activo" checked>
                                    <label class="form-check-label" for="activo">
                                        Producto activo
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="guardarProducto()">
                    <i class="fas fa-save"></i>
                    Guardar
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let productoEditando = null;

function aplicarFiltros() {
    // Implementar filtros aquí
    console.log('Aplicando filtros...');
}

function editarProducto(id) {
    // Cargar datos del producto y abrir modal
    fetch(`/api/productos/${id}/`)
        .then(response => response.json())
        .then(producto => {
            productoEditando = id;
            document.getElementById('tituloModal').textContent = 'Editar Producto';
            
            // Llenar formulario
            document.getElementById('nombre').value = producto.nombre;
            document.getElementById('sku').value = producto.sku;
            document.getElementById('codigo_barras').value = producto.codigo_barras;
            document.getElementById('precio').value = producto.precio;
            document.getElementById('categoria').value = producto.categoria;
            document.getElementById('proveedor').value = producto.proveedor || '';
            document.getElementById('imagen_principal').value = producto.imagen_principal || '';
            document.getElementById('activo').checked = producto.activo;
            
            new bootstrap.Modal(document.getElementById('modalProducto')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al cargar el producto');
        });
}

function verDetalles(id) {
    // Redirigir a vista de detalles o mostrar modal
    window.open(`/api/productos/${id}/`, '_blank');
}

function eliminarProducto(id) {
    if (confirm('¿Está seguro de que desea eliminar este producto?')) {
        fetch(`/api/productos/${id}/`, {
            method: 'DELETE',
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Error al eliminar el producto');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al eliminar el producto');
        });
    }
}

function guardarProducto() {
    const formData = {
        nombre: document.getElementById('nombre').value,
        sku: document.getElementById('sku').value,
        codigo_barras: document.getElementById('codigo_barras').value,
        precio: parseFloat(document.getElementById('precio').value) || 0,
        categoria: parseInt(document.getElementById('categoria').value),
        proveedor: document.getElementById('proveedor').value || null,
        imagen_principal: document.getElementById('imagen_principal').value || null,
        activo: document.getElementById('activo').checked
    };

    const url = productoEditando ? `/api/productos/${productoEditando}/` : '/api/productos/';
    const method = productoEditando ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            return response.json().then(data => {
                throw new Error(JSON.stringify(data));
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al guardar el producto: ' + error.message);
    });
}

// Limpiar formulario al cerrar modal
document.getElementById('modalProducto').addEventListener('hidden.bs.modal', function () {
    productoEditando = null;
    document.getElementById('tituloModal').textContent = 'Nuevo Producto';
    document.getElementById('formProducto').reset();
    document.getElementById('activo').checked = true;
});
</script>
{% endblock %}
